<div class="modal fade" id="editQtyModal" tabindex="-1" role="dialog" aria-labelledby="editQtyModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <a class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                <a class="btn2 btn2-xs btn2-danger delete-item-btn" style="float: right; margin-right: 10px; color:#fff;"><i class="fa fa-trash"></i></a>
                <h4 class="modal-title" id="editQtyModalLabel"><?php echo $this->lang->line('edit_quantity'); ?></h4>
                <small>Tahap pengujian</small>
            </div>
            <form id="editQtyForm" action="<?php echo base_url('admin/bill/updateQuantity'); ?>" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_qty_item_name">Item Saat Ini:</label>
                        <p id="edit_qty_item_name" class="form-control-static"></p>
                    </div>
                    
                    <!-- Ganti text input dengan select dropdown -->
                    <div class="form-group">
                        <label for="edit_item_name">Ganti Item:</label>
                        <select class="form-control select2" id="edit_item_name" name="item_name">
                            <option value="">-- Pilih Item Baru --</option>
                            <?php 
                            // Fetch charges from the database
                            $charges = $this->db->get('charges')->result();
                            foreach ($charges as $charge): ?>
                                <option value="<?php echo $charge->id; ?>" data-price="<?php echo $charge->standard_charge; ?>">
                                    <?php echo $charge->name; ?> (Rp. <?php echo number_format($charge->standard_charge, 0, ',', '.'); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Kosongkan jika tidak ingin mengubah item</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_qty_new"><?php echo $this->lang->line('quantity'); ?>:</label>
                        <input type="number" class="form-control" id="edit_qty_new" name="new_qty" min="1" required>
                    </div>
                    <input type="hidden" id="edit_qty_type" name="type">
                    <input type="hidden" id="edit_qty_id" name="id">
                    <input type="hidden" id="edit_qty_current" name="current_qty">
                    <input type="hidden" name="case_reference_id" value="<?php echo $nota; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo $this->lang->line('cancel'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo $this->lang->line('save'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize select2
    $('#edit_item_name').select2({
        dropdownParent: $('#editQtyModal'),
        width: '100%'
    });
    
    // Set current quantity when modal opens
    $('#editQtyModal').on('show.bs.modal', function(e) {
        var currentQty = $('#edit_qty_current').val();
        $('#edit_qty_new').val(currentQty);
        
        // Reset select2
        $('#edit_item_name').val('').trigger('change');
    });
    
    // When a new item is selected, update price information
    $('#edit_item_name').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
            var newPrice = selectedOption.data('price');
            var newQty = $('#edit_qty_new').val() || 1;
            var newTotal = newPrice * newQty;
            
            // You can display the new price information if needed
            // For example, add a price info element to the form
        }
    });
    
    // Update total when quantity changes
    $('#edit_qty_new').on('change', function() {
        var selectedOption = $('#edit_item_name').find('option:selected');
        if (selectedOption.val()) {
            var newPrice = selectedOption.data('price');
            var newQty = $(this).val() || 1;
            var newTotal = newPrice * newQty;
            
            // Update price info if needed
        }
    });
    
    // Prevent parent modal from closing when this modal is closed
    $('#editQtyModal').on('hidden.bs.modal', function(e) {
        e.stopPropagation();
    });
    
    // Handle delete button click
    $('.delete-item-btn').on('click', function() {
        if (confirm('<?php echo $this->lang->line('delete_confirm'); ?>')) {
            var id = $('#edit_qty_id').val();
            var type = $('#edit_qty_type').val();
            
            $.ajax({
                url: '<?php echo base_url(); ?>admin/bill/deleteItem',
                type: 'POST',
                data: {
                    id: id,
                    type: type
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // Close modal
                        $('#editQtyModal').modal('hide');
                        
                        // Show success message
                        successMsg(response.message);
                        
                        // Trigger refresh
                        setTimeout(function() {
                            refreshBillData();
                        }, 1000);
                    } else {
                        errorMsg(response.message);
                    }
                },
                error: function() {
                    errorMsg('<?php echo $this->lang->line('error_occurred'); ?>');
                }
            });
        }
    });
    
    // Handle form submission via AJAX
    $('#editQtyForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Close the edit modal
                    $('#editQtyModal').modal('hide');
                    
                    // Show success message
                    successMsg(response.message);
                    
                    // Get the case_id from the form
                    var case_id = $('input[name="case_reference_id"]').val();
                    
                    // Close any open modals
                    $('.modal').modal('hide');
                    
                    // Wait for modals to close, then trigger the showbill function
                    setTimeout(function() {
                        // Create a temporary element with the case_id data attribute
                        var tempElement = $('<a>', {
                            'data-case-id': case_id
                        });
                        
                        // Trigger the showbill function with the temporary element
                        $.ajax({
                            type: 'POST',
                            url: base_url+'admin/bill/patient_bill',
                            data: {case_reference_id: case_id},
                            dataType: 'json',
                            beforeSend: function() {
                                // No loading button to set here
                            },
                            success: function (result) {                
                                $("#patient_bill_summary").html(result.patient_name);
                                $("#billSummaryData").html(result.page);
                                $('#billSummaryModal .modal_action').html(result.modal_action);
                                $("#billSummaryModal").modal("show");
                            },
                            error: function(xhr) {
                                alert("<?php echo $this->lang->line('error_occurred_please_try_again'); ?>");    
                            }
                        });
                    }, 500);
                } else {
                    // Show error message
                    errorMsg(response.message);
                }
            },
            error: function() {
                errorMsg('<?php echo $this->lang->line('error_occurred'); ?>');
            }
        });
    });
});

// Function to refresh bill data without page reload
function refreshBillData() {
    // Get the case reference ID from the form
    var caseReferenceId = $('input[name="case_reference_id"]').val();
    
    // Reload the bill content via AJAX
    $.ajax({
        url: '<?php echo base_url(); ?>admin/bill/getBillDetails',
        type: 'POST',
        data: {
            case_reference_id: caseReferenceId
        },
        success: function(response) {
            // Update the bill content in the parent modal
            $('.bill-details-content').html(response);
        },
        error: function() {
            errorMsg('<?php echo $this->lang->line('error_occurred'); ?>');
        }
    });
}
</script>






<?php $currency_symbol = $this->customlib->getHospitalCurrencyFormat(); ?>
 

 



<!-- Modal Diskon -->
<div class="modal fade" id="discountModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Diskon</h4>
            </div>
            <div class="modal-body">
                <form id="discountForm">
                    <input type="hidden" id=bill_no name="bill_no" value="<?php echo $nota?>">
                    <div class="form-group">
                        <label>Ju<PERSON>lah <PERSON></label>
                        <input type="number" class="form-control" name="discount_amount" id="discount_amount" value="<?php echo isset($discount) ? $discount : 0; ?>">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="saveDiscount">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- Script JavaScript -->
<script type="text/javascript">


$(document).ready(function() {
    // Inisialisasi modal
    $('#discountModal').modal({
        backdrop: 'static',
        keyboard: false,
        show: false
    });
    
    // Fungsi untuk membuka modal
    window.editDiscount = function() {
        $('#discountModal').modal('show');
    };

    // Handler untuk tombol simpan
    $('#saveDiscount').click(function() {
        var formData = {
            bill_no: $('input[name="bill_no"]').val(),
            discount_amount: $('#discount_amount').val()
        };

        // Validasi input
        if (!formData.bill_no || !formData.discount_amount) {
            alert('Nomor Bill dan Jumlah Diskon harus diisi');
            return;
        }

        // Log data yang akan dikirim
        console.log('Sending data:', formData);

        $(this).prop('disabled', true);
        
        $.ajax({
            url: '<?php echo base_url("admin/bill_diskon/save"); ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Response:', response);
                if(response.success) {
                    $('#discountModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.message || 'Gagal menyimpan diskon');
                }
            },
            error: function(xhr, status, error) {
                console.error('Ajax error:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);
                
                let errorMessage = 'Gagal menyimpan diskon';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch(e) {
                    errorMessage += ': ' + error;
                }
                
                alert(errorMessage);
            },
            complete: function() {
                $('#saveDiscount').prop('disabled', false);
            }
        });
    });
});
</script>

<?php
// Hitung total tagihan
$total_bill_query = $this->db->query("
    SELECT 
        COALESCE(SUM(
            CASE 
                WHEN pc.ipd_id IS NOT NULL OR pc.opd_id IS NOT NULL 
                THEN pc.amount 
                ELSE 0 
            END
        ), 0) as total_charges
    FROM patient_charges pc
    WHERE 
        (pc.ipd_id IN (SELECT id FROM ipd_details WHERE case_reference_id = ?) 
        OR pc.opd_id IN (SELECT id FROM opd_details WHERE case_reference_id = ?))
", array($nota, $nota));

$total_bill = $total_bill_query->row()->total_charges;

// Ambil jumlah diskon
$discount_query = $this->db->query("
    SELECT COALESCE(SUM(discount_amount), 0) as total_discount 
    FROM bill_discounts 
    WHERE bill_no = ?
", array($nota));

$total_discount = $discount_query->row()->total_discount;

// Kurangi total tagihan dengan diskon
$total_after_discount = $total_bill - $total_discount;

// Hitung total pembayaran dan info kasir
$payment_query = $this->db->query("
    SELECT 
        COUNT(DISTINCT kasir) as kasir_count,
        COALESCE(SUM(amount), 0) as total_paid,
        GROUP_CONCAT(DISTINCT 
            CASE 
                WHEN s.name IS NOT NULL THEN s.name 
                ELSE 'Unknown'
            END
        ) as kasir_names,
        MAX(payment_date) as last_payment_date
    FROM transactions t
    LEFT JOIN staff s ON s.id = t.kasir
    WHERE t.case_reference_id = ? 
    AND t.type = 'payment'
    AND t.kasir IS NOT NULL
", array($nota));

$payment_data = $payment_query->row();

// Tentukan status pembayaran berdasarkan total setelah diskon
$is_lunas = ($payment_data->kasir_count > 0 && $payment_data->total_paid >= $total_after_discount);
$status_text = $is_lunas ? "LUNAS" : "BELUM LUNAS";
$status_color = $is_lunas ? "#28a745" : "#dc3545";
?>

<!-- Tambahkan setelah row status pembayaran yang sudah ada -->
<div class="row mb-3">
    <div class="col-md-12">
        <div class="payment-info-box" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 20px;">
            <div class="row">
                <div class="col-md-6">
                    <strong>STATUS PEMBAYARAN:</strong>
                    <span style="color: <?php echo $status_color; ?>; font-weight: bold; font-size: 16px; margin-left: 10px;">
                        <?php echo $status_text; ?>
                    </span>
                </div>
                <div class="col-md-6 text-right">
                    <strong>TANGGAL PEMBAYARAN TERAKHIR:</strong>
                    <span style="margin-left: 10px;">
                        <?php 
                        echo $payment_data->last_payment_date ? 
                            date('d/m/Y H:i', strtotime($payment_data->last_payment_date)) : 
                            '-';
                        ?>
                    </span>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <strong>DIPROSES OLEH KASIR:</strong>
                    <span style="margin-left: 10px;">
                        <?php 
                        echo $payment_data->kasir_names ? 
                            strtoupper($payment_data->kasir_names) : 
                            '-';
                        ?>
                    </span>
                </div>
            </div>
            <?php if (!$is_lunas && $payment_data->total_paid > 0): ?>
            <div class="row mt-2">
                <div class="col-md-12">
                    <strong>SISA YANG HARUS DIBAYAR:</strong>
                    <span style="margin-left: 10px;">
                        <?php echo $currency_symbol . number_format($total_bill - $payment_data->total_paid, 2, ',', '.'); ?>
                    </span>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

 

<!-- Tambahkan ini di bagian atas, sebelah kiri -->
<div class="row mb-3">
    <div class="col-md-6">
        <h4 class="<?php echo $status_class; ?>" style="font-size: 24px; font-weight: bold;">
            <?php echo $payment_status; ?>
        </h4>
    </div>
    <div class="col-md-6 text-right">
        <?php if (!$is_lunas): ?>
            <button class="btn btn-success btn-sm" onclick="showPaymentModal()">
                <i class="fa fa-money"></i> Tambah Pembayaran
            </button>
        <?php endif; ?>
        <button class="btn btn-primary btn-sm" onclick="editDiscount()">
            <i class="fa fa-tags"></i> Edit Diskon
        </button>
        <div class="checkbox" style="display: inline-block; margin-left: 10px;">
            <label>
                <input type="checkbox" id="opd_only" name="opd_only" <?php echo isset($_SESSION['opd_saja']) && $_SESSION['opd_saja'] ? 'checked' : ''; ?> onclick="toggleOpdOnly()"> Hanya Cetak Rawat Jalan
            </label>
        </div>

        <div class="checkbox" style="display: inline-block; margin-left: 10px;">
            <label>
                <input type="checkbox" id="hide_farmasi" name="hide_farmasi" <?php echo isset($_SESSION['farmasi_hide']) && $_SESSION['farmasi_hide'] ? 'checked' : ''; ?> onclick="toggleFarmasi()"> Sembunyikan Farmasi
            </label>
        </div>

        <div class="checkbox" style="display: inline-block; margin-left: 10px;">
            <label>
                <input type="checkbox" id="hide_labor" name="hide_labor" <?php echo isset($_SESSION['labor_hide']) && $_SESSION['labor_hide'] ? 'checked' : ''; ?> onclick="toggleLabor()"> Sembunyikan Laboratorium
            </label>
        </div>

        <div class="checkbox" style="display: inline-block; margin-left: 10px;">
            <label>
                <input type="checkbox" id="hide_radio" name="hide_radio" <?php echo isset($_SESSION['radio_hide']) && $_SESSION['radio_hide'] ? 'checked' : ''; ?> onclick="toggleRadio()"> Sembunyikan Radiologi
            </label>
        </div>
    </div>
</div>

<script>
function showPaymentModal() {
    // Sembunyikan modal patient_bill
    $('#billModal').modal('hide');
    
    // Set nilai ke form yang ada di index.php
    <?php if(!empty($opd_data)) { ?>
        $('#module_id').val('<?php echo $opd_data[0]['opd_id']; ?>');
        $('#module_name').val('opd');
    <?php } else if(!empty($ipd_data)) { ?>
        $('#module_id').val('<?php echo $ipd_data[0]['ipd_id']; ?>');
        $('#module_name').val('ipd');
    <?php } ?>
    $('#case_reference_id').val('<?php echo $nota; ?>');
    
    // Tampilkan modal pembayaran
    $('#myPaymentModal').modal({
        backdrop: 'static',
        keyboard: false,
        show: true
    });
}

// Handler ketika modal pembayaran ditutup
$(document).on('hidden.bs.modal', '#myPaymentModal', function () {
    // Tampilkan kembali modal patient_bill dengan backdrop yang benar
    $('#billModal').modal({
        backdrop: 'static',
        keyboard: false,
        show: true
    });
});

// Pastikan modal patient_bill tetap bisa diinteraksi
$(document).ready(function() {
    $('#billModal').css('pointer-events', 'auto');
});

function toggleOpdOnly() {
    var isChecked = $('#opd_only').is(':checked');
    
    // Simpan status ke session via AJAX
    $.ajax({
        url: '<?php echo base_url("admin/bill/set_opd_only"); ?>',
        type: 'POST',
        data: {opd_only: isChecked ? 1 : 0},
        dataType: 'json',
        success: function(response) {
            console.log('Session updated:', response);
        }
    });
}
</script>
<script>
function toggleFarmasi() {
    var isChecked = $('#hide_farmasi').is(':checked');
    
    // Kirim AJAX request untuk menyimpan preferensi di session
    $.ajax({
        url: '<?php echo base_url("admin/bill/toggleFarmasi"); ?>',
        type: 'POST',
        data: {hide_farmasi: isChecked ? 1 : 0},
        success: function(response) {
            // Reload halaman untuk menerapkan perubahan
           // location.reload();
        }
    });
}


function toggleLabor() {
        var isChecked = $('#hide_labor').is(':checked');
        $.ajax({
            url: '<?php echo base_url(); ?>admin/bill/toggleLabor',
            type: 'POST',
            data: {hide_labor: isChecked ? 1 : 0},
            success: function(response) {
               // location.reload();
            }
        });
    }

    function toggleRadio() {
        var isChecked = $('#hide_radio').is(':checked');
        $.ajax({
            url: '<?php echo base_url(); ?>admin/bill/toggleRadio',
            type: 'POST',
            data: {hide_radio: isChecked ? 1 : 0},
            success: function(response) {
                //location.reload();
            }
        });
    }


</script>
<style>
/* Atur z-index untuk semua komponen modal */
#billModal {
    z-index: 1040 !important;
}
#billModal .modal-backdrop {
    z-index: 1039 !important;
}
#myPaymentModal {
    z-index: 1060 !important;
}
#myPaymentModal .modal-backdrop {
    z-index: 1059 !important;
}
/* Pastikan modal bisa diklik */
.modal {
    pointer-events: auto !important;
}
/* Atur opacity backdrop agar tidak terlalu gelap */
.modal-backdrop.in {
    opacity: 0.5;
}
</style>

<?php 
$total_amount=0;
$amount_paid=0;
$amount_refund = 0;
if(!empty($opd_data)){
 
    ?>
    <h4><?php echo $this->lang->line('opd_charges'); ?></h4>
<div class="table-responsive">    
<table class="table table-hover">
    <thead>
        <tr>
            <th width="35%"><?php echo $this->lang->line('service'); ?></th>
            <th width="15%"><?php echo $this->lang->line('charge'); ?></th>
            <th width="10%"><?php echo $this->lang->line('qty'); ?></th>
            <th width="12%" class="text-right"><?php echo $this->lang->line('discount'); ?></th>
            <th class="text-right" width="13%"><?php echo $this->lang->line('tax'); ?></th>
            <th class="text-right" width="15%"><?php echo $this->lang->line('amount'); ?></th>
            <th width="5%" class="text-center"><?php echo $this->lang->line('action'); ?></th>
        </tr>
    </thead>
    <tbody>
    <?php 
    foreach ($opd_data as $opd_key => $opd_value) {
        $total_amount+=$opd_value['amount'];
        $this->db->query("update patient_charges set amount='".$opd_value['standard_charge']."' ,apply_charge='".$opd_value['standard_charge']."'  where id='".$opd_value['id']."'");
 ?>
      <tr>
         
          <td width="35%">  
            <?php echo $opd_value['idx'];?> - 
            <?php echo $opd_value['name'];?>
                        
                    </td>
                    <td width="15%">
            <?php echo $currency_symbol.number_format($opd_value['apply_charge'],2,',','.');?>
                    </td>
                    <td width="10%">
                        
            <?php echo $opd_value['qty']; //." ".$opd_value['unit'] ?>
                    </td>
                    <td width="12%" class="text-right"><?php echo number_format(0,2,',','.'); ?></td>
                    <td class=" text-right">
                        <?php echo number_format($opd_value['tax'],2,',','.');?>

                    </td>
                    
                    <td class="text text-right">
                
            <?php echo $currency_symbol.number_format($opd_value['amount'],2,',','.');?>
                    </td>
                    <td class="text-center">
                        <button class="btn btn-xs btn-info" onclick="editQty('opd', <?php echo $opd_value['id']; ?>, <?php echo $opd_value['qty']; ?>, '<?php echo $opd_value['name']; ?>')">
                            <i class="fa fa-pencil"></i>
                        </button>
                    </td>
      </tr>

      <?php

    }
       ?>
   </tbody>
</table>
</div>
    <?php 
}

if(!empty($ipd_data)){
 
    ?>
    <h4><?php echo $this->lang->line('ipd_charges'); ?></h4>
<div class="table-responsive">    
<table class="table table-hover">
    <thead>
        <tr>
            <th width="35%"><?php echo $this->lang->line('service'); ?></th>
            <th width="15%"><?php echo $this->lang->line('charge'); ?></th>
            <th width="10%"><?php echo $this->lang->line('qty'); ?></th>
            <th width="12%" class="text-right"><?php echo $this->lang->line('discount'); ?></th>
            <th class="text-right" width="13%"><?php echo $this->lang->line('tax'); ?></th>
            <th class="text-right" width="15%"><?php echo $this->lang->line('amount'); ?></th>
            <th width="5%" class="text-center"><?php echo $this->lang->line('action'); ?></th>
        </tr>
    </thead>
    <tbody>
    <?php 
    foreach ($ipd_data as $ipd_key => $ipd_value) {
        $total_amount+=$ipd_value['amount'];
 ?>
      <tr>
         
          <td width="35%">
            <?php echo $ipd_value['idx'];?>
<?php echo $ipd_value['name'];?>
              
          </td>
          <td width="15%">
<?php echo $currency_symbol.number_format($ipd_value['apply_charge'],2,',','.');?> 
          </td>
          <td width="10%">
              
<?php echo $ipd_value['qty']." ".$ipd_value['unit'];?>
          </td>
          <td width="12%" class="text-right"><?php echo number_format(0,2,',','.'); ?></td>
          <td class="text text-right" width="13%">
            <?php echo number_format($ipd_value['tax'],2,',','.');?>

          </td>
          
           <td class="text text-right">
      
<?php echo $currency_symbol.number_format($ipd_value['amount'],2,',','.');?>
          </td>
          <td class="text-center">
            <button class="btn btn-xs btn-info" onclick="editQty('ipd', <?php echo $ipd_value['id']; ?>, <?php echo $ipd_value['qty']; ?>, '<?php echo $ipd_value['name']; ?>')">
                <i class="fa fa-pencil"></i>
            </button>
          </td>
      </tr>

      <?php

    }
       ?>
   </tbody>
</table>
</div>
    <?php 
}

//=========Pharmacy==========
if(!empty($pharmacy_data)){
    ?>
      <h4><?php echo $this->lang->line('pharmacy_bill'); ?></h4>
<div class="table-responsive">      
<table class="table table-hover">
    <thead>
        <tr>
            <th width="15%"><?php echo $this->lang->line('bill_no'); ?></th>
            <th width="35%">Nama Obat</th>
            <th width="10%"><?php echo $this->lang->line('qty'); ?></th>
            <th width="12%"><?php echo $this->lang->line('charge'); ?></th>
            <th class="text-right" width="13%"><?php echo $this->lang->line('discount'); ?></th>
            <th class="text-right" width="15%"><?php echo $this->lang->line('amount'); ?></th>
        </tr>
    </thead>
    <tbody>
    <?php 
    foreach ($pharmacy_data as $pharmacy_key => $pharmacy_value) {
        $total_amount += $pharmacy_value->net_amount;
        
        // Get medicine details using the new SQL query
        $sql = "SELECT pharmacy_bill_detail.*,medicine_batch_details.expiry,medicine_batch_details.pharmacy_id,medicine_batch_details.batch_no,medicine_batch_details.tax,pharmacy.medicine_name,pharmacy.unit,pharmacy.id as `medicine_id`,pharmacy.medicine_category_id,medicine_category.medicine_category FROM `pharmacy_bill_detail` INNER JOIN medicine_batch_details on medicine_batch_details.id=pharmacy_bill_detail.medicine_batch_detail_id INNER JOIN pharmacy on pharmacy.id= medicine_batch_details.pharmacy_id INNER JOIN medicine_category on medicine_category.id= pharmacy.medicine_category_id WHERE pharmacy_bill_basic_id =" . $this->db->escape($pharmacy_value->id);
        $query = $this->db->query($sql);
        $medicine_details = $query->result_array();
        
        // First row with bill number
        ?>
        <tr>
            <td width="15%" class="white-space-nowrap"><?php echo $pharmacy_bill_prefix.$pharmacy_value->id;?></td>
            <td colspan="6"></td>
        </tr>
        <?php
        // Show medicine details
        foreach($medicine_details as $med) {
            $subtotal = $med['quantity'] * $med['sale_price'];
            ?>
            <tr>
                <td></td>
                <td>
                    <?php echo !empty($med['medicine_name']) ? $med['medicine_name'] : "-"; ?>
                    <small class="text-muted d-block" style="display:none;">
                        <?php echo !empty($med['medicine_category']) ? $med['medicine_category'] : ""; ?>
                        <?php echo !empty($med['batch_no']) ? " | Batch: ".$med['batch_no'] : ""; ?>
                        <?php echo !empty($med['unit']) ? " | ".$med['unit'] : ""; ?>
                    </small>
                </td>
                <td><?php echo $med['quantity']; ?></td>
                <td><?php echo $currency_symbol.number_format($med['sale_price'],2,',','.'); ?></td>
                <td class="text-right">-</td>
                <td class="text text-right"><?php echo $currency_symbol.number_format($subtotal,2,',','.'); ?></td>
            </tr>
            <?php
        }
        // Show total row
        ?>
        <tr>
            <td colspan="4"></td>
            <td class="text-right"><?php echo "(".$pharmacy_value->discount_percentage."%) ".$currency_symbol.number_format($pharmacy_value->discount,2,',','.');?></td>
            <td class="text text-right"><?php echo $currency_symbol.number_format($pharmacy_value->net_amount,2,',','.');?></td>
        </tr>
        <?php
    }
    ?>
    </tbody>
</table>
</div>
    <?php
}

?>
<?php
//====================Pathology Billing================

if(!empty($pathology_data)){
    ?>
     <h4><?php echo $this->lang->line('pathology_bill'); ?></h4>
<div class="table-responsive">     
<table class="table table-hover">
    <thead>
        <tr>
            <th width="35%"><?php echo $this->lang->line('test_name'); ?></th>
            <th width="15%"><?php echo $this->lang->line('charge'); ?></th>
            <th width="10%"><?php echo $this->lang->line('qty'); ?></th>
            <th width="12%" class="text-right"><?php echo $this->lang->line('discount'); ?></th>
            <th class="text-right" width="13%"><?php echo $this->lang->line('tax'); ?></th>
            <th class="text-right" width="15%"><?php echo $this->lang->line('amount'); ?></th>
        </tr>
    </thead>
    <tbody>
      <?php 
        foreach ($pathology_data as $pathology_key => $pathology_value) {
          $total_amount+=$pathology_value->net_amount;
      ?>
        <tr>
            <td width="35%" class="white-space-nowrap"><?php echo $pathology_value->test_name;?></td>
            <td width="15%"><?php echo  $currency_symbol.$pathology_value->total;?></td>
            <td width="10%">1</td>
            <td width="12%"  class="text text-right"><?php  echo "(".$pathology_value->discount_percentage."%) ". $currency_symbol.$pathology_value->discount;?></td>
            <td width="13%" class="text text-right"><?php echo $currency_symbol.$pathology_value->tax;?></td>
            <td class="text text-right"><?php echo $currency_symbol.$pathology_value->net_amount;?></td>
        </tr>
       <?php
    }
    ?>
    </tbody>
  </table>
</div>  
    <?php
}      

//====================Radiology Billing================

if(!empty($radiology_data)){
    ?>
     <h4><?php echo $this->lang->line('radiology_bill'); ?></h4>
<div class="table-responsive">     
<table class="table table-hover">
    <thead>
        <tr>
            <th width="35%"><?php echo $this->lang->line('test_name'); ?></th>
            <th width="15%"><?php echo $this->lang->line('charge'); ?></th>
            <th width="10%"><?php echo $this->lang->line('qty'); ?></th>
            <th width="12%" class="text-right"><?php echo $this->lang->line('discount'); ?></th>
            <th class="text-right" width="13%"><?php echo $this->lang->line('tax'); ?></th>
            <th class="text-right" width="15%"><?php echo $this->lang->line('amount'); ?></th>
        </tr>
    </thead>
    <tbody>
      <?php 
    foreach ($radiology_data as $radiology_key => $radiology_value) {
           $total_amount+=$radiology_value->net_amount;
      ?>
       <td width="35%" class="white-space-nowrap">  <?php echo $radiology_value->test_name;?> </td>
        <td width="15%"> <?php echo  $currency_symbol.$radiology_value->total;?> </td>
        <td width="10%" >  1     </td>
        <td width="12%" class="text text-right"> <?php  echo "(".$radiology_value->discount_percentage."%) ". $currency_symbol.$radiology_value->discount;?>            
       </td>
        <td class="text text-right" width="13%" ><?php echo $currency_symbol.$radiology_value->tax;?> </td>
       <td class="text text-right" > <?php echo $currency_symbol.$radiology_value->net_amount;?> </td>
       <?php

    }
    ?>
    </tbody>
  </table>
</div>  
    <?php
}      

//====================Blood Issue================

if(!empty($bloodissue_data)){
    ?>
     <h4><?php echo $this->lang->line('blood_issue'); ?></h4>
<div class="table-responsive">     
<table class="table table-hover">
    <thead>
        <tr>
            <th width="20%"><?php echo $this->lang->line('bill_no'); ?></th>
            <th width="20%"><?php echo $this->lang->line('charge'); ?></th>
            <th width="10%"><?php echo $this->lang->line('qty'); ?></th>
            <th class="text text-right" width="15%"><?php echo $this->lang->line('discount'); ?></th>
            <th class="text text-right"  width="15%"><?php echo $this->lang->line('tax'); ?></th>
            <th class="text text-right"><?php echo $this->lang->line('amount'); ?></th>
        </tr>
    </thead>
    <tbody>
      <?php 
    foreach ($bloodissue_data as $blood_issue_key => $blood_issue_value) {
        $total_amount+=$blood_issue_value->net_amount;
   
$discount_amount=calculatePercent($blood_issue_value->standard_charge,$blood_issue_value->discount_percentage);
      ?>
      <tr>
       <td width="20%"> <?php echo $blood_bank_bill_prefix.$blood_issue_value->id;?>  </td>
         <td width="20%"> <?php echo  $currency_symbol.$blood_issue_value->standard_charge;?> </td>
       <td width="10%">  1    </td>
       <td width="15%" class="text text-right">  <?php  echo "(".$blood_issue_value->discount_percentage."%) ". $discount_amount;?></td>
       <td width="15%" class="text text-right">
        <?php 
           echo "(".$blood_issue_value->tax_percentage."%) ". $currency_symbol.calculatePercent(($blood_issue_value->standard_charge-$discount_amount),$blood_issue_value->tax_percentage);
           ?>
      </td>
      <td class="text text-right">  <?php echo $currency_symbol.$blood_issue_value->net_amount;?>  </td>
   </tr>
       <?php

    }
    ?>
  


    </tbody>
  </table>
</div>  
    <?php
}  ?>

<?php   
$tutup=0;
if($tutup==1){  
    if(!empty($transaction_data)){
        ?>
 <h4><?php echo $this->lang->line('transactions'); ?></h4>
<div class="table-responsive"> 
<table class="table table-hover">
    <thead>
        <tr>
            <th><?php echo $this->lang->line('transaction_id'); ?></th>
            <th><?php echo $this->lang->line('payment_date'); ?></th>
            <th><?php echo $this->lang->line('payment_mode'); ?></th>
            <th class="text text-right"><?php echo $this->lang->line('amount'); ?></th>
           
        </tr>
    </thead>
    <tbody>
            <?php
    foreach ($transaction_data as $transaction_key => $transaction_value) {
        $amount_paid+=$transaction_value->amount;
            ?>
            <tr>
            <td width="20%" class="white-space-nowrap"><?php echo $transaction_prefix.$transaction_value->id;?></td>
            <td width="30%"><?php echo $this->customlib->YYYYMMDDHisTodateFormat($transaction_value->payment_date);?></td>
            <td><?php echo $this->lang->line(strtolower($transaction_value->payment_mode));?></td>
               <td class="text text-right"><?php echo $currency_symbol.$transaction_value->amount;?></td>
           </tr>

            <?php
        }
        ?>
    </tbody>
</table>
</div>
        <?php
    }
    
}
 ?>

<?php    
$tutup=0;
if($tutup==1){ 
    if(!empty($refund_data)){
        ?>
 <h4><?php echo $this->lang->line('refund'); ?></h4>
<div class="table-responsive"> 
<table class="table table-hover">
    <thead>
        <tr>
            <th><?php echo $this->lang->line('transaction_id'); ?></th>
            <th><?php echo $this->lang->line('payment_date'); ?></th>
            <th><?php echo $this->lang->line('payment_mode'); ?></th>
            <th class="text text-right"><?php echo $this->lang->line('amount'); ?></th>
           
        </tr>
    </thead>
    <tbody>
            <?php
            
    foreach ($refund_data as $transaction_key => $transaction_value) {
        $amount_refund+=$transaction_value->amount;
            ?>
            <tr>
            <td width="20%" class="white-space-nowrap"><?php echo $transaction_prefix.$transaction_value->id;?></td>
            <td width="30%"><?php echo $this->customlib->YYYYMMDDHisTodateFormat($transaction_value->payment_date);?></td>
            <td><?php echo $this->lang->line(strtolower($transaction_value->payment_mode));?></td>
               <td class="text text-right"><?php echo $currency_symbol.$transaction_value->amount;?></td>
           </tr>

            <?php
        }
        ?>
    </tbody>
</table>
</div>
        <?php
    
}
}
 ?>

                         <!-- ====transaction data========= -->
    
<div class="row">
    <div class="col-md-6">
        
    </div>
  <div class="col-md-6">
           <p class="lead"><?php echo $this->lang->line('amount_summary'); ?></p>
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <tbody>
                        <tr>
                        <th style="width:50%"><?php echo $this->lang->line('grand_total'); ?>:</th>
                        <td class="text text-right"><?php echo $currency_symbol.number_format($total_amount,2,',','.'); ?></td>
                      </tr>
                    
                      <?php 
                      // Ambil diskon menggunakan nomor nota
                      $discount_amount = 0;
                      $discount_query = $this->db->query("SELECT discount_amount FROM bill_discounts WHERE bill_no = ?", array($nota));
                      $discount_row = $discount_query->row();
                      $discount_amount = ($discount_row) ? $discount_row->discount_amount : 0;
                      
                      if($discount_amount > 0) { 
                          $total_after_discount = $total_amount - $discount_amount;
                      ?>
                      <tr>
                          <th>Diskon:</th>
                          <td class="text text-right"><?php echo $currency_symbol.number_format($discount_amount,2,',','.'); ?></td>
                      </tr>
                      <tr>
                          <th>Total Setelah Diskon:</th>
                          <td class="text text-right"><?php echo $currency_symbol.number_format($total_after_discount,2,',','.'); ?></td>
                      </tr>
                      <?php } else {
                          $total_after_discount = $total_amount;
                      } ?>

                      <tr>
                        <th><?php echo $this->lang->line('amount_paid'); ?>:</th>
                        <td class="text text-right"><?php echo $currency_symbol.number_format($amount_paid,2,',','.'); ?></td>
                      </tr>

                      <tr>
                        <th><?php echo $this->lang->line('balance_amount'); ?>:</th>
                        <td class="text text-right"><?php echo $currency_symbol.number_format(($total_after_discount-$amount_paid),2,',','.');?></td>
                      </tr>
                    </tbody></table>
                  </div>
                </div>
</div>                  
                    </div>
                </div>   
<?php
// Include modal for editing quantity
include(APPPATH . 'views/admin/bill/_edit_qty_modal.php');
?>

<script>
function editQty(type, id, currentQty, itemName) {
    // Set values in the modal
    $('#edit_qty_type').val(type);
    $('#edit_qty_id').val(id);
    $('#edit_qty_current').val(currentQty);
    $('#edit_qty_item_name').text(itemName);
    
    // Show the modal
    $('#editQtyModal').modal('show');
}



// Add this script at the end of your existing JavaScript
$(document).ready(function() {
    // Prevent editQtyModal from closing the parent modal
    $('#editQtyModal').on('hidden.bs.modal', function(e) {
        e.stopPropagation();
        // Ensure the parent modal stays visible and active
        $('#billModal').modal('show');
        $('body').addClass('modal-open');
    });
    
    // Make sure the backdrop doesn't close the parent modal when clicking outside the edit modal
    $(document).on('click', '.modal-backdrop', function(e) {
        if ($('#editQtyModal').hasClass('in')) {
            e.stopPropagation();
            return false;
        }
    });
});
</script>

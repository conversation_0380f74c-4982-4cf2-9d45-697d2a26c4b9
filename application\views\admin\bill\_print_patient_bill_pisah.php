<link rel="stylesheet" href="<?php echo base_url(); ?>backend/dist/css/sh-print.css">
<style>
/* Font definitions */
body {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-size: 11px;
    line-height: 1.3;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.header-line1,
.header-line2,
.header-line3 {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-weight: bold;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.header-line4 {
    font-family: 'Arial', 'Helvetica', sans-serif;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.noborder_table {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-size: 11px;
    line-height: 1.3;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.noborder_table th {
    font-weight: 600;
    font-family: 'Arial', 'Helvetica', sans-serif;
}

.noborder_table td {
    font-family: 'Arial', 'Helvetica', sans-serif;
}

h4 {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-size: 11px;
    font-weight: 600;
    margin: 10px 0 5px;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

/* Print specific font adjustments */
@media print {
    body {
        font-family: 'Arial', 'Helvetica', sans-serif !important;
    }

    .header-line1,
    .header-line2,
    .header-line3 {
        font-size: 11px !important;
        line-height: 1.2 !important;
        letter-spacing: 0.03em !important;
    }

    .header-line4 {
        font-size: 11px !important;
        line-height: 1.2 !important;
        letter-spacing: 0.02em !important;
    }

    .noborder_table {
        font-size: 11px !important;
        line-height: 1.3 !important;
        letter-spacing: 0.02em !important;
    }

    h4 {
        font-size: 11px !important;
        line-height: 1.2 !important;
    }
}

/* Header styles */
.header {
    width: 100%;
    padding: 3px 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0.3cm;
    gap: 5px;
}

.logo-column {
    flex: 0 0 35px;
}

.logo img {
    width: 35px;
    height: auto;
    padding: 0;
}

.text-column {
    flex: 1;
    text-align: left;
    min-width: 0;
    padding-left: 5px;
}

.header-line1 {
    font-size: 11px;
    font-weight: bold;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    line-height: 1.1;
    letter-spacing: 0.03em;
}

.header-line2 {
    font-size: 11px;
    font-weight: bold;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    line-height: 1.1;
    letter-spacing: 0.03em;
}

.header-line3 {
    font-size: 11px;
    font-weight: bold;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    line-height: 1.1;
    letter-spacing: 0.03em;
}

.header-line4 {
    font-size: 11px;
    color: #333;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    line-height: 1.1;
    letter-spacing: 0.02em;
}

/* Other styles */
.print-area {
    font-size: 11px;
    line-height: 1.1;
    text-transform: uppercase;
}

.noborder_table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
    margin-top: 3px;
    font-family: Arial, sans-serif;
    line-height: 1.1;
}

.noborder_table th,
.noborder_table td {
    padding: 0px 2px;
    line-height: 1.1;
    vertical-align: top;
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow content */
    text-overflow: ellipsis; /* Show ... for overflow text */
}

/* Khusus untuk kolom keterangan, biarkan bisa wrap dan lebih lebar */
.noborder_table td:first-child {
    white-space: normal; /* Allow text wrapping */
    width: 50%; /* Increase width for keterangan column */
    max-width: 50%; /* Set maximum width */
    word-wrap: break-word; /* Break long words if needed */
}

/* Adjust other columns accordingly */
.noborder_table td:nth-child(2) { /* Petugas column */
    width: 20%; /* Reduced width */
}
.noborder_table td:nth-child(3) { /* Harga column */
    width: 8%;
}
.noborder_table td:nth-child(4) { /* Jumlah column */
    width: 5%;
}
.noborder_table td:nth-child(5) { /* Disc column */
    width: 7%;
}
.noborder_table td:nth-child(6) { /* Total column */
    width: 10%;
}

.noborder_table th {
    font-size: 11px;
    font-weight: bold;
    letter-spacing: 0.03em;
}

h4 {
    font-size: 11px;
    margin: 5px 0 2px 0;
    font-weight: bold;
    font-family: Arial, sans-serif;
    line-height: 1.1;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.card-body {
    padding: 10px;
}

.border_top,
.border_bottom {
    font-size: 11px;
}

/* Tambahkan media print untuk memastikan ukuran saat print */
@media print {
    .header {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;

        padding-left: 10px;
    }

    .logo img {
        width: 35px !important;
    }

    .text-column {
        padding-left: 3px !important;
    }

    .header-line1, .header-line2, .header-line3 {
        font-size: 11px !important;
        line-height: 1 !important;
        letter-spacing: 0.03em !important;
    }

    .header-line4 {
        font-size: 11px !important;
        line-height: 1 !important;
        letter-spacing: 0.02em !important;
    }

    .print-area {
        width: 100%;
        margin: 0;
        padding: 0;
    }

    .noborder_table {
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .noborder_table th,
    .noborder_table td {
        padding: 0px 2px !important;
        line-height: 1.2 !important;
    }

    h4 {
        line-height: 1.2 !important;
    }
}

/* Update bill header styles */
.bill-header {
    display: flex;
    justify-content: flex-end; /* Changed from space-between to flex-end */
    align-items: center;
    margin-bottom: 3px;
    font-size: 11px;
    line-height: 1.2;
}

.bill-title {
    margin-right: 5px; /* Add spacing between title and number */
}

.bill-number {
    font-weight: bold;
    min-width: 100px; /* Ensure consistent width */
    text-align: right;
}

/* Header Styles */
.bill-header {
    margin-bottom: 20px;
    border-bottom: 2px solid #000;
}

.header-table {
    width: 100%;
    border-collapse: collapse;
}

.logo-cell {
    width: 15%;
    padding: 10px;
    vertical-align: top;
}

.logo-img {
    max-width: 100px;
    height: auto;
}

.hospital-info {
    width: 50%;
    padding: 10px;
    vertical-align: top;
}

.hospital-name {
    font-size: 11pt;
    font-weight: bold;
    margin: 0 0 5px 0;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.hospital-address {
    font-size: 11pt;
    line-height: 1.3;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.bill-info {
    width: 35%;
    padding: 10px;
    vertical-align: top;
}

.bill-details {
    width: 100%;
    font-size: 11pt;
    text-transform: uppercase;
}

.bill-details td {
    padding: 2px 4px;
}

/* Total Section Styles */
.total-section {
    margin-top: 20px;
    margin-bottom: 20px;
    page-break-inside: avoid;
}

.total-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.total-table tr {
    border-bottom: 1px dotted #ddd;
}

.total-label {
    text-align: right;
    padding: 8px 15px;
    font-size: 11pt;
    font-weight: bold;
    width: 80%;
    text-transform: uppercase;
}

.total-amount {
    text-align: right;
    padding: 8px 15px;
    font-size: 11pt;
    text-transform: uppercase;
}

.grand-total {
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    background-color: #f9f9f9;
}

.grand-total .total-label,
.grand-total .total-amount {
    font-weight: bold;
    font-size: 11pt;
    padding: 10px 15px;
    text-transform: uppercase;
}

/* Print Specific Styles */
@media print {
    .bill-header {
        margin-bottom: 20px;
    }

    .total-section {
        page-break-inside: avoid;
    }

    .grand-total {
        background-color: transparent !important;
    }

    .page-break {
        page-break-before: always;
    }

    .page-1 {
        page-break-after: always;
    }
}
</style>
<?php $currency_symbol = $this->customlib->getHospitalCurrencyFormat(); ?>

<?php
// Cek apakah session opd_saja ada dan bernilai true
$opd_saja = isset($_SESSION['opd_saja']) && $_SESSION['opd_saja'] ? true : false;
// Cek apakah session farmasi_hide ada dan bernilai true
$farmasi_hide = isset($_SESSION['farmasi_hide']) && $_SESSION['farmasi_hide'] ? true : false;
// Cek apakah session labor_hide ada dan bernilai true
$labor_hide = isset($_SESSION['labor_hide']) && $_SESSION['labor_hide'] ? true : false;
// Cek apakah session radio_hide ada dan bernilai true
$radio_hide = isset($_SESSION['radio_hide']) && $_SESSION['radio_hide'] ? true : false;
?>

<div class="print-area">
<div class="header">
    <div class="logo-column">
        <div class="logo">
            <img width=110 src="<?=base_url()?>uploads/hospital_content/logo/1mini_logo.png?1694398170" alt="Logo">
        </div>
    </div>
    <div class="text-column">
        <div class="header-line1">KEPOLISIAN REPUBLIK INDONESIA</div>
        <div class="header-line2">DAERAH SUMATERA SELATAN</div>
        <div class="header-line3">RUMAH SAKIT BAYANGKARA M HASAN</div>
        <div class="header-line4">JLN. JENDRAL SUDIRMAN KM 4.5 PALEMBANG 30128 TELP. (0711) 414855</div>

    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body" style="font-family: Arial, sans-serif;">  <!-- Added font-family -->
                <table class="noborder_table" cellspacing="0" cellpadding="0" style="width: 100%; font-size: 11px; line-height: 1.3; text-transform: uppercase;"> <!-- Added font styling -->
                <tbody>
                    <tr>
                        <th width="15%" style="font-weight: 600;">No RM</th> <!-- Modified font-weight -->
                        <td width="40%">: <?php echo $patient['patient_id'];?></td>
                        <th width="13%" style="font-weight: 600;">Nota Tagihan</th>
                        <td width="30%">: <b><?=$case_id?></b></td>
                    </tr>
                    <tr>
                        <th>Jadwal Kunjungan</th>
                        <td colspan="3">: <?php if($patient['appointment_date'] !='' && $patient['appointment_date']!='0000-00-00'){
                            echo $this->customlib->YYYYMMDDHisTodateFormat($patient['appointment_date'],$this->customlib->getHospitalTimeFormat());
                        } ?></td>
                    </tr>
                    <?php
                        $dok = $this->db->query("select c.case_reference_id,b.name,b.surname from visit_details a left join staff b on a.cons_doctor = b.id
                                    left join opd_details c on c.id=a.opd_details_id
                                    where c.case_reference_id = '$case_id'")->row_array();

                        $dokipd = $this->db->query("select * from ipd_details a left join staff b on b.id=a.cons_doctor where a.case_reference_id='$case_id'")->row_array();
                    ?>
                    <tr>
                        <th><?php echo $this->lang->line('name'); ?></th>
                        <td>: <?php echo composePatientName($patient['patient_name'],$patient['patient_id']); ?></td>
                        <th><?php echo $this->lang->line('guardian_name'); ?></th>
                        <td>: <?php echo $patient['guardian_name']; ?></td>
                    </tr>
                    <tr>
                        <th>Dokter</th>
                        <td>: <?php
                            if (!empty($patient['doctor_name'])) {
                                echo $patient['doctor_name'];
                                if (!empty($patient['doctor_surname'])) {
                                    echo ' ' . $patient['doctor_surname'];
                                }
                            } else {
                                echo $dok['name'] ?? '-';
                            }
                        ?></td>
                        <th>Usia</th>
                        <td>: <?php echo $patient['age']." Tahun ".$patient['month']." Bulan ".$patient['day']." Hari"; ?></td>
                    </tr>
                    <tr>
                        <th><?php echo $this->lang->line('phone'); ?></th>
                        <td>: <?php echo $patient['mobileno']; ?></td>
                        <th></th>
                        <td></td>
                    </tr>
                    <?php
                    if($patient['ipdid']!='' && $patient['ipdid']!=0){?>
                    <tr>
                        <th>No Rawat Inap</th>
                        <td colspan="3">: <?php
                            if($patient['ipdid']!='' && $patient['ipdid']!=0){
                                echo $this->customlib->getSessionPrefixByType('ipd_no').$patient['ipdid'];
                            }

                            if ($patient['discharged'] == 'yes') {
                                echo " <span class='label label-warning'>" . $this->lang->line("discharged") . "</span>";
                            }
                            ?>
                        </td>
                    </tr>
                <?php } ?>
                    <?php
                    if($patient['opdid']!='' && $patient['opdid']!=0){?>
                    <tr>
                        <th>No Rawat Jalan</th>
                        <td colspan="3">: <?php
                            if($patient['opdid']!='' && $patient['opdid']!=0){
                                // Get OPD number
                                echo $this->customlib->getSessionPrefixByType('opd_no').$patient['opdid'];

                                // Get Poli name with correct table relationships
                                $this->db->select('staff.name, staff.surname, department.department_name as poli_name');
                                $this->db->from('opd_details opd');
                                $this->db->join('visit_details vd', 'opd.id = vd.opd_details_id', 'left');
                                $this->db->join('staff', 'vd.cons_doctor = staff.id', 'left');
                                $this->db->join('department', 'vd.id_depar = department.id', 'left');
                                $this->db->where('opd.id', $patient['opdid']);
                                $query = $this->db->get();
                                $opd_details = $query->row_array();

                                if(!empty($opd_details['poli_name'])) {
                                    echo " - " . $opd_details['poli_name'];
                                }
                            }
                            ?>
                        </td>
                    </tr>
                <?php } ?>
                <?php
                // Detail rawat inap akan ditampilkan di halaman 2, jadi tidak perlu di halaman 1
                // Hanya simpan data untuk digunakan di halaman 2
                if($patient['ipdid'] !='' && $patient['ipdid'] !=0 && !$opd_saja){
                    $inap = $this->db->query("select ipd_update.*, staff.name as doctor_name from ipd_update left join staff on staff.id = ipd_update.cons_doctor where ipd_id = '$patient[ipdid]' order by ipd_update.id desc limit 1")->row_array();
                    $nmdok = $this->db->query("select a.*, b.name as doctor_name from ipd_details a left join staff b on b.id = a.cons_doctor where a.id = '$patient[ipdid]'")->row_array();
                }
                ?>
                </tbody>
            </table>


<?php
$total_amount = 0;
$amount_paid = 0;

// Inisialisasi variabel subtotal untuk setiap kategori
$subtotal_opd = 0;
$subtotal_ipd = 0;
$subtotal_pharmacy = 0;
$subtotal_pathology = 0;
$subtotal_radiology = 0;

if(!empty($opd_data)){
?>
    <h4 style="padding-left:3px; font-family: Arial, sans-serif; font-size: 11px; font-weight: 600; margin: 10px 0 5px; text-transform: uppercase; letter-spacing: 0.03em;">RAWAT JALAN</h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="35%" style="font-weight: 600; padding: 4px 0;">KETERANGAN</th>
            <th width="35%" style="font-weight: 600; padding: 4px 0;">PETUGAS</th>
            <th width="8%" style="font-weight: 600; padding: 4px 0;" class="text-right">HARGA</th>
            <th width="5%" style="font-weight: 600; padding: 4px 0;" class="text-center">JUMLAH</th>
            <th width="7%" style="font-weight: 600; padding: 4px 0;" class="text-right">DISC</th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;" class="text-right">TOTAL</th>
        </tr>
    </thead>
    <?php

    foreach ($opd_data as $opd_key => $opd_value) {
        $petugas=$this->db->where('id',$opd_value['petugas'])->get('staff')->row();
        $totalx = $opd_value['standard_charge'] * $opd_value['qty'];
        $subtotal_opd += $totalx; // Tambahkan ke subtotal rawat jalan
        $total_amount += $totalx;
    ?>
    <tr style="line-height: 1.4;">
        <td style="padding: 3px 0;"><?php echo $opd_value['name'];?></td>
        <td style="padding: 3px 0;"><?php echo !empty($petugas->name) ? $petugas->name : "-"; ?></td>
        <td style="padding: 3px 0;" class="text-right"><?php echo number_format($opd_value['standard_charge'],0,',','.'); ?></td>
        <td style="padding: 3px 0;" class="text-center"><?php echo $opd_value['qty']; ?></td>
        <td style="padding: 3px 0;" class="text-right">0</td>
        <td style="padding: 3px 0;" class="text-right"><?php echo number_format($totalx,0,',','.'); ?></td>
    </tr>
    <?php } ?>
    <!-- Tambahkan baris subtotal -->
    <tr class="border_top" style="font-weight: bold;">
        <td colspan="5" class="text-right">SUBTOTAL RAWAT JALAN</td>
        <td class="text-right"><?php echo number_format($subtotal_opd,0,',','.'); ?></td>
    </tr>
    </table>
<?php }

// Rawat inap akan ditampilkan di halaman 2, jadi tidak perlu di halaman 1
// Tetapi tetap hitung subtotal untuk total keseluruhan
if(!empty($ipd_data) && !$opd_saja){
    foreach ($ipd_data as $ipd_key => $ipd_value) {
        $totalx = $ipd_value['standard_charge'] * $ipd_value['qty'];
        $subtotal_ipd += $totalx; // Tambahkan ke subtotal rawat inap
        $total_amount += $totalx;
    }
}

//=========Pharmacy FRJ (Rawat Jalan)==========
if(!empty($pharmacy_data) && !$farmasi_hide){
    // Pisahkan data farmasi berdasarkan FRJ dan FRI
    $pharmacy_frj = array();
    $pharmacy_fri = array();

    foreach ($pharmacy_data as $pharmacy_value) {
        // Cek apakah resep ini berasal dari rawat jalan atau rawat inap
        $prescription_check = $this->db->query("
            SELECT ipb.ipd_id, ipb.visit_details_id
            FROM ipd_prescription_basic ipb
            WHERE ipb.id = (
                SELECT ipd_prescription_basic_id
                FROM pharmacy_bill_basic
                WHERE id = " . $this->db->escape($pharmacy_value->id) . "
            )
        ")->row();

        if ($prescription_check) {
            if (!empty($prescription_check->visit_details_id) && empty($prescription_check->ipd_id)) {
                // FRJ - Rawat Jalan
                $pharmacy_frj[] = $pharmacy_value;
            } elseif (!empty($prescription_check->ipd_id)) {
                // FRI - Rawat Inap
                $pharmacy_fri[] = $pharmacy_value;
            }
        }
    }

    // Tampilkan Farmasi FRJ (Rawat Jalan) di halaman 1
    if(!empty($pharmacy_frj)) {
    ?>
    <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;">FARMASI (FRJ - RAWAT JALAN)</h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="35%" style="font-weight: 600; padding: 4px 0;">KETERANGAN</th>
            <th width="35%" style="font-weight: 600; padding: 4px 0;">PETUGAS</th>
            <th width="8%" style="font-weight: 600; padding: 4px 0;" class="text-right">HARGA</th>
            <th width="5%" style="font-weight: 600; padding: 4px 0;" class="text-center">JUMLAH</th>
            <th width="7%" style="font-weight: 600; padding: 4px 0;" class="text-right">DISC</th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;" class="text-right">TOTAL</th>
        </tr>
    </thead>
    <?php
    $subtotal_pharmacy_frj = 0;
    foreach ($pharmacy_frj as $pharmacy_key => $pharmacy_value) {
        // Get petugas info
        $petugas = $this->db->select('staff.name')
                           ->from('pharmacy_bill_basic')
                           ->join('staff', 'staff.id = pharmacy_bill_basic.generated_by', 'left')
                           ->where('pharmacy_bill_basic.id', $pharmacy_value->id)
                           ->get()
                           ->row();

        $sql = "SELECT pharmacy_bill_detail.*,medicine_batch_details.expiry,
                medicine_batch_details.pharmacy_id,medicine_batch_details.batch_no,
                pharmacy.medicine_name,pharmacy.unit,pharmacy.id as medicine_id
                FROM pharmacy_bill_detail
                INNER JOIN medicine_batch_details on medicine_batch_details.id=pharmacy_bill_detail.medicine_batch_detail_id
                INNER JOIN pharmacy on pharmacy.id= medicine_batch_details.pharmacy_id
                WHERE pharmacy_bill_basic_id =" . $this->db->escape($pharmacy_value->id);
        $query = $this->db->query($sql);
        $medicine_details = $query->result_array();
    ?>
        <tr>
            <td colspan="6"><b> No Resep : FRJ<?php echo $pharmacy_value->id;?></b></td>
        </tr>
        <?php
        $pharmacy_bill_subtotal = 0; // Inisialisasi subtotal untuk resep ini
        foreach($medicine_details as $med) {
            $subtotal = $med['quantity'] * $med['sale_price'];
            $pharmacy_bill_subtotal += $subtotal; // Tambahkan ke subtotal resep
        ?>
        <tr>
            <td>
                <?php echo !empty($med['medicine_name']) ? strtoupper($med['medicine_name']) : "-"; ?>
                <small style="font-size:70%; color:#666;">
                    <?php
                    $tampil = $this->db->query("SELECT catatan FROM ipd_prescription_details
                        where pharmacy_id='$med[pharmacy_id]' and
                        basic_id='$catat->ipd_prescription_basic_id'")->row();
                    echo !empty($tampil->catatan) ? $tampil->catatan : "-";
                    ?>
                </small>
            </td>
            <td><?php // echo !empty($petugas->name) ? $petugas->name : "-"; ?></td>
            <td class="text-right"><?php echo number_format($med['sale_price'],0,',','.'); ?></td>
            <td class="text-center"><?php echo $med['quantity']; ?></td>
            <td class="text-right">0</td>
            <td class="text-right"><?php echo number_format($subtotal,0,',','.'); ?></td>
        </tr>
        <?php } ?>
        <?php
        // Tambahkan subtotal farmasi FRJ ke total keseluruhan
        $subtotal_pharmacy_frj += $pharmacy_bill_subtotal;
        $subtotal_pharmacy += $pharmacy_bill_subtotal;
        $total_amount += $pharmacy_bill_subtotal;
        ?>
    <?php } ?>
    <!-- Tambahkan baris subtotal FRJ -->
    <tr class="border_top" style="font-weight: bold;">
        <td colspan="5" class="text-right">SUBTOTAL FARMASI FRJ</td>
        <td class="text-right"><?php echo number_format($subtotal_pharmacy_frj,0,',','.'); ?></td>
    </tr>
    </table>
    <?php
    }
}

//====================Pathology Billing================

if(!empty($pathology_data) && !$labor_hide){
    ?>
     <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;">LABORATORIUM</h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="35%" style="font-weight: 600; padding: 4px 0;">KETERANGAN</th>
            <th width="35%" style="font-weight: 600; padding: 4px 0;">PETUGAS</th>
            <th width="8%" style="font-weight: 600; padding: 4px 0;" class="text-right">HARGA</th>
            <th width="5%" style="font-weight: 600; padding: 4px 0;" class="text-center">JUMLAH</th>
            <th width="7%" style="font-weight: 600; padding: 4px 0;" class="text-right">DISC</th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;" class="text-right">TOTAL</th>
        </tr>
    </thead>
    <tbody>
        <?php
        foreach ($pathology_data as $pathology_key => $pathology_value) {
            $subtotal_pathology += $pathology_value->net_amount; // Tambahkan ke subtotal laboratorium
            $total_amount += $pathology_value->net_amount;

            // Get petugas info
            $petugas = $this->db->select('staff.name')
                               ->from('pathology_billing')
                               ->join('staff', 'staff.id = pathology_billing.generated_by', 'left')
                               ->where('pathology_billing.id', $pathology_value->id)
                               ->get()
                               ->row();

            // Get test details
            $test_details = $this->db->query("SELECT a.id, a.pathology_id, '1' as qty,
                                            a.apply_charge, b.test_name
                                            FROM pathology_report a
                                            LEFT JOIN pathology b ON b.id=a.pathology_id
                                            WHERE a.pathology_bill_id=" . $pathology_value->id)
                                   ->result();
        ?>
        <tr>
            <td colspan="6"><?php echo $pathology_bill_prefix.$pathology_value->id;?></td>
        </tr>
        <?php foreach($test_details as $test) { ?>
            <tr>
                <td><?php echo $test->test_name; ?></td>
                <td>-</td>
                <td class="text-right"><?php echo number_format($test->apply_charge,0,',','.'); ?></td>
                <td class="text-center"><?php echo $test->qty; ?></td>
                <td class="text-right">0</td>
                <td class="text-right"><?php echo number_format($test->apply_charge,0,',','.'); ?></td>
            </tr>
        <?php } ?>
    <?php
    }
    ?>
    <!-- Tambahkan baris subtotal -->
    <tr class="border_top" style="font-weight: bold;">
        <td colspan="5" class="text-right">SUBTOTAL LABORATORIUM</td>
        <td class="text-right"><?php echo number_format($subtotal_pathology,0,',','.'); ?></td>
    </tr>
    </table>
    <?php
}

//====================Radiology Billing================
if(!empty($radiology_data) && !$radio_hide){
    ?>
     <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;">RADIOLOGI</h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="35%" style="font-weight: 600; padding: 4px 0;">KETERANGAN</th>
            <th width="35%" style="font-weight: 600; padding: 4px 0;">PETUGAS</th>
            <th width="8%" style="font-weight: 600; padding: 4px 0;" class="text-right">HARGA</th>
            <th width="5%" style="font-weight: 600; padding: 4px 0;" class="text-center">JUMLAH</th>
            <th width="7%" style="font-weight: 600; padding: 4px 0;" class="text-right">DISC</th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;" class="text-right">TOTAL</th>
        </tr>
    </thead>
    <tbody>
        <?php
        foreach ($radiology_data as $radiology_key => $radiology_value) {
           $subtotal_radiology += $radiology_value->net_amount; // Tambahkan ke subtotal radiologi
           $total_amount += $radiology_value->net_amount;

           // Get petugas info
           $petugas = $this->db->select('staff.name')
                              ->from('radiology_billing')
                              ->join('staff', 'staff.id = radiology_billing.generated_by', 'left')
                              ->where('radiology_billing.id', $radiology_value->id)
                              ->get()
                              ->row();
        ?>
        <tr>
            <td><?php echo $radiology_bill_prefix.$radiology_value->id;?></td>
            <td><?php echo !empty($petugas->name) ? $petugas->name : "-"; ?></td>
            <td class="text-right"><?php echo number_format($radiology_value->total,0,',','.'); ?></td>
            <td class="text-center">1</td>
            <td class="text-right"><?php echo "(".$radiology_value->discount_percentage."%) ". number_format($radiology_value->discount,0,',','.'); ?></td>
            <td class="text-right"><?php echo number_format($radiology_value->net_amount,0,',','.'); ?></td>
        </tr>
        <?php
        }
        ?>
    </tbody>
    <!-- Tambahkan baris subtotal -->
    <tr class="border_top" style="font-weight: bold;">
        <td colspan="5" class="text-right">SUBTOTAL RADIOLOGI</td>
        <td class="text-right"><?php echo number_format($subtotal_radiology,0,',','.'); ?></td>
    </tr>
  </table>
    <?php
}

// Cek apakah ada rawat inap untuk menentukan apakah perlu halaman 2
$has_ipd = (!empty($ipd_data) && !$opd_saja);
$has_pharmacy_fri = false;

// Cek apakah ada farmasi FRI
if(!empty($pharmacy_data) && !$farmasi_hide && isset($pharmacy_fri) && !empty($pharmacy_fri)){
    $has_pharmacy_fri = true;
}

// Jika ada rawat inap atau farmasi FRI, tambahkan page break
if($has_ipd || $has_pharmacy_fri) {
?>
</div> <!-- End halaman 1 -->

<!-- HALAMAN 2 - RAWAT INAP -->
<div class="page-break">
<div class="print-area">
<div class="header">
    <div class="logo-column">
        <div class="logo">
            <img width=110 src="<?=base_url()?>uploads/hospital_content/logo/1mini_logo.png?1694398170" alt="Logo">
        </div>
    </div>
    <div class="text-column">
        <div class="header-line1">KEPOLISIAN REPUBLIK INDONESIA</div>
        <div class="header-line2">DAERAH SUMATERA SELATAN</div>
        <div class="header-line3">RUMAH SAKIT BAYANGKARA M HASAN</div>
        <div class="header-line4">JLN. JENDRAL SUDIRMAN KM 4.5 PALEMBANG 30128 TELP. (0711) 414855</div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body" style="font-family: Arial, sans-serif;">
                <table class="noborder_table" cellspacing="0" cellpadding="0" style="width: 100%; font-size: 11px; line-height: 1.3; text-transform: uppercase;">
                <tbody>
                    <tr>
                        <th width="15%" style="font-weight: 600;">No RM</th>
                        <td width="40%">: <?php echo $patient['patient_id'];?></td>
                        <th width="13%" style="font-weight: 600;">Nota Tagihan</th>
                        <td width="30%">: <b><?=$case_id?></b></td>
                    </tr>
                    <tr>
                        <th><?php echo $this->lang->line('name'); ?></th>
                        <td>: <?php echo composePatientName($patient['patient_name'],$patient['patient_id']); ?></td>
                        <th><?php echo $this->lang->line('guardian_name'); ?></th>
                        <td>: <?php echo $patient['guardian_name']; ?></td>
                    </tr>
                    <?php
                    // Tampilkan detail rawat inap di halaman 2
                    if($patient['ipdid']!='' && $patient['ipdid']!=0){?>
                    <tr>
                        <th>No Rawat Inap</th>
                        <td colspan="3">: <?php
                            if($patient['ipdid']!='' && $patient['ipdid']!=0){
                                echo $this->customlib->getSessionPrefixByType('ipd_no').$patient['ipdid'];
                            }

                            if ($patient['discharged'] == 'yes') {
                                echo " <span class='label label-warning'>" . $this->lang->line("discharged") . "</span>";
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo $this->lang->line('admission_date'); ?></th>
                        <td>: <?php if($patient['date']!='' && $patient['date']!='0000-00-00'){
                            echo $this->customlib->YYYYMMDDTodateFormat($patient['date']);
                        } ?></td>
                        <th><?php echo $this->lang->line('bed'); ?></th>
                        <td>: <?php
                            $bnd = $patient['bed_name'] . " - " . $patient['bedgroup_name'] . " - " . $patient['floor_name'];
                            echo !empty($inap['catatan']) ? $inap['catatan'] : " $bnd ";
                        ?></td>
                    </tr>
                    <tr>
                        <th>Tanggal Keluar</th>
                        <td>: <?php
                            $tanggal = !empty($inap['tanggal_keluar']) ? date('d/m/Y', strtotime($inap['tanggal_keluar'])) : '';
                            echo $tanggal;
                        ?></td>
                        <th>Dokter</th>
                        <td>: <?php echo isset($inap['doctor_name']) ? $inap['doctor_name'] : '-'; ?></td>
                    </tr>
                    <?php } ?>
                </tbody>
                </table>

<?php
// Tampilkan bagian rawat inap di halaman 2
if(!empty($ipd_data) && !$opd_saja){ ?>
    <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;">RAWAT INAP</h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="35%" style="font-weight: 600; padding: 4px 0;">KETERANGAN</th>
            <th width="35%" style="font-weight: 600; padding: 4px 0;">PETUGAS</th>
            <th width="8%" style="font-weight: 600; padding: 4px 0;" class="text-right">HARGA</th>
            <th width="5%" style="font-weight: 600; padding: 4px 0;" class="text-center">JUMLAH</th>
            <th width="7%" style="font-weight: 600; padding: 4px 0;" class="text-right">DISC</th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;" class="text-right">TOTAL</th>
        </tr>
    </thead>
    <?php
    $subtotal_ipd_page2 = 0;
    foreach ($ipd_data as $ipd_key => $ipd_value) {
        $petugas=$this->db->where('id',$ipd_value['petugas'])->get('staff')->row();
        $totalx = $ipd_value['standard_charge'] * $ipd_value['qty'];
        $subtotal_ipd_page2 += $totalx;
    ?>
    <tr>
        <td><?php echo $ipd_value['name'];?></td>
        <td><?php echo !empty($petugas->name) ? $petugas->name : "-"; ?></td>
        <td class="text-right"><?php echo number_format($ipd_value['standard_charge'],0,',','.'); ?></td>
        <td class="text-center"><?php echo $ipd_value['qty']; ?></td>
        <td class="text-right">0</td>
        <td class="text-right"><?php echo number_format( $totalx ,0,',','.'); ?></td>
    </tr>
    <?php } ?>
    <!-- Tambahkan baris subtotal -->
    <tr class="border_top" style="font-weight: bold;">
        <td colspan="5" class="text-right">SUBTOTAL RAWAT INAP</td>
        <td class="text-right"><?php echo number_format($subtotal_ipd_page2,0,',','.'); ?></td>
    </tr>
    </table>
<?php }

// Tampilkan Farmasi FRI (Rawat Inap) di halaman 2
if(!empty($pharmacy_data) && !$farmasi_hide && isset($pharmacy_fri) && !empty($pharmacy_fri)) {
?>
    <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;">FARMASI (FRI - RAWAT INAP)</h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="35%" style="font-weight: 600; padding: 4px 0;">KETERANGAN</th>
            <th width="35%" style="font-weight: 600; padding: 4px 0;">PETUGAS</th>
            <th width="8%" style="font-weight: 600; padding: 4px 0;" class="text-right">HARGA</th>
            <th width="5%" style="font-weight: 600; padding: 4px 0;" class="text-center">JUMLAH</th>
            <th width="7%" style="font-weight: 600; padding: 4px 0;" class="text-right">DISC</th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;" class="text-right">TOTAL</th>
        </tr>
    </thead>
    <?php
    $subtotal_pharmacy_fri = 0;
    foreach ($pharmacy_fri as $pharmacy_key => $pharmacy_value) {
        // Get petugas info
        $petugas = $this->db->select('staff.name')
                           ->from('pharmacy_bill_basic')
                           ->join('staff', 'staff.id = pharmacy_bill_basic.generated_by', 'left')
                           ->where('pharmacy_bill_basic.id', $pharmacy_value->id)
                           ->get()
                           ->row();

        $sql = "SELECT pharmacy_bill_detail.*,medicine_batch_details.expiry,
                medicine_batch_details.pharmacy_id,medicine_batch_details.batch_no,
                pharmacy.medicine_name,pharmacy.unit,pharmacy.id as medicine_id
                FROM pharmacy_bill_detail
                INNER JOIN medicine_batch_details on medicine_batch_details.id=pharmacy_bill_detail.medicine_batch_detail_id
                INNER JOIN pharmacy on pharmacy.id= medicine_batch_details.pharmacy_id
                WHERE pharmacy_bill_basic_id =" . $this->db->escape($pharmacy_value->id);
        $query = $this->db->query($sql);
        $medicine_details = $query->result_array();
    ?>
        <tr>
            <td colspan="6"><b> No Resep : FRI<?php echo $pharmacy_value->id;?></b></td>
        </tr>
        <?php
        $pharmacy_bill_subtotal = 0; // Inisialisasi subtotal untuk resep ini
        foreach($medicine_details as $med) {
            $subtotal = $med['quantity'] * $med['sale_price'];
            $pharmacy_bill_subtotal += $subtotal; // Tambahkan ke subtotal resep
        ?>
        <tr>
            <td>
                <?php echo !empty($med['medicine_name']) ? strtoupper($med['medicine_name']) : "-"; ?>
                <small style="font-size:70%; color:#666;">
                    <?php
                    $tampil = $this->db->query("SELECT catatan FROM ipd_prescription_details
                        where pharmacy_id='$med[pharmacy_id]' and
                        basic_id='$catat->ipd_prescription_basic_id'")->row();
                    echo !empty($tampil->catatan) ? $tampil->catatan : "-";
                    ?>
                </small>
            </td>
            <td><?php // echo !empty($petugas->name) ? $petugas->name : "-"; ?></td>
            <td class="text-right"><?php echo number_format($med['sale_price'],0,',','.'); ?></td>
            <td class="text-center"><?php echo $med['quantity']; ?></td>
            <td class="text-right">0</td>
            <td class="text-right"><?php echo number_format($subtotal,0,',','.'); ?></td>
        </tr>
        <?php } ?>
        <?php
        // Tambahkan subtotal farmasi FRI
        $subtotal_pharmacy_fri += $pharmacy_bill_subtotal;
        ?>
    <?php } ?>
    <!-- Tambahkan baris subtotal FRI -->
    <tr class="border_top" style="font-weight: bold;">
        <td colspan="5" class="text-right">SUBTOTAL FARMASI FRI</td>
        <td class="text-right"><?php echo number_format($subtotal_pharmacy_fri,0,',','.'); ?></td>
    </tr>
    </table>
<?php }

// Tambahkan signature di halaman 2
?>
    <table class="signature-table" style="text-transform: uppercase;">
    <tr>
    <td>
            <div class="signature-cell">
                PALEMBANG, <?//= date('d-m-Y') ?>
                <br>MENYERAHKAN
                <div class="signature-line"></div><br><br><br>
                <?php
                // Query untuk mendapatkan nama kasir
                $transaction_query = $this->db->select('t.kasir, s.name as staff_name')
                                            ->from('transactions t')
                                            ->join('staff s', 's.id = t.kasir', 'left')
                                            ->where('t.case_reference_id', $case_id)
                                            ->order_by('t.payment_date', 'DESC')
                                            ->limit(1)
                                            ->get();

                $kasir_data = $transaction_query->row();

                if(!empty($kasir_data)) {
                    $kasir_name = !empty($kasir_data->staff_name) ? $kasir_data->staff_name : ".......................................";
                    echo "( <b>" . strtoupper($kasir_name) . "</b> )";
                } else {
                    echo "(.......................................)";
                }
                ?>
            </div>
        </td>

        <td>
            <div class="signature-cell">
            PALEMBANG, <?php
$tanggal = !empty($inap['tanggal_keluar']) ? date('d/m/Y', strtotime($inap['tanggal_keluar'])) : '';
echo $tanggal;
?><br>
            YANG MENERIMA

                <div class="signature-line"></div><br><br>

                 <br>
                <b> (  <?= strtoupper($patient['patient_name']) ?> ) </b>
            </div>
        </td>
    </tr>
</table>

            </div>
        </div>
    </div>
</div>
</div> <!-- End halaman 2 -->

<?php } ?>

//====================Blood Issue================

<?php
if(!empty($bloodissue_data)){
    ?>
     <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;"><?php echo strtoupper($this->lang->line('blood_issue')); ?></h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="20%" style="font-weight: 600; padding: 4px 0;"><?php echo strtoupper($this->lang->line('bill_no')); ?></th>
            <th width="20%" style="font-weight: 600; padding: 4px 0;"><?php echo strtoupper($this->lang->line('charge')); ?></th>
            <th width="10%" style="font-weight: 600; padding: 4px 0;">JUMLAH</th>
            <th width="15%" style="font-weight: 600; padding: 4px 0;"><?php echo strtoupper($this->lang->line('discount')); ?></th>
            <th width="15%" style="font-weight: 600; padding: 4px 0;" class="text-right"><?php echo strtoupper($this->lang->line('tax')); ?></th>
            <th width="20%" style="font-weight: 600; padding: 4px 0;" class="text-right"><?php echo strtoupper($this->lang->line('amount')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php
        foreach ($bloodissue_data as $blood_issue_key => $blood_issue_value) {
        $total_amount+=$blood_issue_value->net_amount;

        $discount_amount=calculatePercent($blood_issue_value->standard_charge,$blood_issue_value->discount_percentage);
        ?>
        <tr>
            <td width="20%"><?php echo $blood_bank_bill_prefix.$blood_issue_value->id;?></td>
            <td width="20%"><?php echo  number_format($blood_issue_value->standard_charge,0,',','.'); ?></td>
            <td width="10%">1</td>
            <td width="15%"><?php echo "(".$blood_issue_value->discount_percentage."%) ". $discount_amount;?></td>
            <td class="text-right"><?php echo "(".$blood_issue_value->tax_percentage."%) ". number_format(calculatePercent(($blood_issue_value->standard_charge-$discount_amount),$blood_issue_value->tax_percentage),0,',','.');
               ?></td>
            <td class="text-right"><?php echo number_format($blood_issue_value->net_amount,0,',','.'); ?></td>
        </tr>
        <?php
        }
        ?>
    </tbody>
  </table>
    <?php
    }
    if(!empty($transaction_data)){
        $tutup= 0;
        if($tutup ==1){
    ?>
    <h4 style="padding-left:3px; text-transform: uppercase; letter-spacing: 0.03em;"><?php echo strtoupper($this->lang->line('transactions')); ?></h4>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
    <thead>
        <tr class="border_top border_bottom">
            <th width="25%" style="font-weight: 600; padding: 4px 0;"><?php echo strtoupper($this->lang->line('transaction_id')); ?></th>
            <th width="25%" style="font-weight: 600; padding: 4px 0;"><?php echo strtoupper($this->lang->line('payment_date')); ?></th>
            <th width="25%" style="font-weight: 600; padding: 4px 0;"><?php echo strtoupper($this->lang->line('payment_mode')); ?></th>
            <th width="25%" style="font-weight: 600; padding: 4px 0;" class="text-right"><?php echo strtoupper($this->lang->line('amount')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php
        foreach ($transaction_data as $transaction_key => $transaction_value) {
        $amount_paid+=$transaction_value->amount;
        ?>
        <tr>
            <td><?php echo $transaction_prefix.$transaction_value->id;?></td>
            <td><?php echo $this->customlib->YYYYMMDDHisTodateFormat($transaction_value->payment_date);?></td>
            <td><?php echo $this->lang->line(strtolower($transaction_value->payment_mode));?></td>
            <td class="text text-right"><?php echo number_format($transaction_value->amount,0,',','.'); ?></td>
        </tr>
        <?php
        }
        ?>
    </tbody>
</table>
    <?php
        }
    }
    ?>
    <hr>
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
        <tbody>
            <tr>
                <th style="width:70%; font-weight: 600; padding: 4px 0;" class="text text-right"><?php echo strtoupper($this->lang->line('grand_total')); ?>:</th>
                <td style="font-weight: 600; padding: 4px 0;" class="text text-right"><?php echo number_format($total_amount,0,',','.'); ?></td>
            </tr>
            <?php 
            // Ambil diskon menggunakan nomor nota
            $discount_query = $this->db->query("SELECT COALESCE(SUM(discount_amount), 0) as total_discount FROM bill_discounts WHERE bill_no = ?", array($case_id));
            $discount = $discount_query->row()->total_discount;
            $total_after_discount = $total_amount - $discount;
            
            if($discount > 0): ?>
            <tr>
                <th style="font-weight: 600; padding: 4px 0;" class="text text-right">DISKON (NO. <?php echo $case_id; ?>):</th>
                <td style="padding: 4px 0;" class="text text-right"><?php echo number_format($discount,0,',','.'); ?></td>
            </tr>
            <tr>
                <th style="font-weight: 600; padding: 4px 0;" class="text text-right">TOTAL SETELAH DISKON:</th>
                <td style="padding: 4px 0;" class="text text-right"><?php echo number_format($total_after_discount,0,',','.'); ?></td>
            </tr>
            <?php else:
                $total_after_discount = $total_amount;
            endif; ?>
            <tr>
                <th style="font-weight: 600; padding: 4px 0;" class="text text-right"><?php echo strtoupper($this->lang->line('amount_paid')); ?>:</th>
                <td style="padding: 4px 0;" class="text text-right"><?php echo number_format($amount_paid,0,',','.'); ?></td>
            </tr>
            <tr>
                <th style="font-weight: 600; padding: 4px 0;" class="text text-right"><?php echo strtoupper($this->lang->line('balance_amount')); ?>:</th>
                <td style="padding: 4px 0;" class="text text-right"><?php echo number_format(($total_after_discount-$amount_paid),0,',','.');?></td>
            </tr>
        </tbody>
    </table>
<hr>
    <!-- Add Terbilang -->
    <table class="noborder_table" style="font-size: 11px; line-height: 1.3; text-transform: uppercase;">
        <tbody>
            <tr>
                <td colspan="2" style="padding-top:5px">
                    <i>TERBILANG: <?php echo strtoupper(terbilang($total_amount))." RUPIAH"; ?></i>
                </td>
            </tr>
        </tbody>
    </table>
    <!-- End Terbilang -->

    <!-- Tambahkan Status Pembayaran -->
    <?php
    // Hitung total tagihan
    $total_bill_query = $this->db->query("
        SELECT 
            COALESCE(SUM(
                CASE 
                    WHEN pc.ipd_id IS NOT NULL OR pc.opd_id IS NOT NULL 
                    THEN pc.amount 
                    ELSE 0 
                END
            ), 0) as total_charges
        FROM patient_charges pc
        WHERE 
            (pc.ipd_id IN (SELECT id FROM ipd_details WHERE case_reference_id = ?) 
            OR pc.opd_id IN (SELECT id FROM opd_details WHERE case_reference_id = ?))
    ", array($case_id, $case_id));

    $total_bill = $total_bill_query->row()->total_charges;

    // Hitung total pembayaran dan info kasir
    $payment_query = $this->db->query("
        SELECT 
            COUNT(DISTINCT kasir) as kasir_count,
            COALESCE(SUM(amount), 0) as total_paid,
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN s.name IS NOT NULL THEN s.name 
                    ELSE 'Unknown'
                END
            ) as kasir_names,
            MAX(payment_date) as last_payment_date
        FROM transactions t
        LEFT JOIN staff s ON s.id = t.kasir
        WHERE t.case_reference_id = ? 
        AND t.type = 'payment'
        AND t.kasir IS NOT NULL
    ", array($case_id));

    $payment_data = $payment_query->row();

    // Tentukan status pembayaran
    $is_lunas = ($payment_data->kasir_count > 0 && $payment_data->total_paid >= $total_bill);
    $status_text = $is_lunas ? "LUNAS" : "BELUM LUNAS";
    $status_color = $is_lunas ? "#28a745" : "#dc3545";
    ?>
<!--
    <div style="margin-top: 10px; margin-bottom: 10px; border: 1px solid #ddd; padding: 10px; font-size: 11px;">
        <table class="noborder_table" style="width: 100%;">
            <tr>
                <td style="width: 50%;">
                    <strong>STATUS PEMBAYARAN:</strong>
                    <span style="color: <?php echo $status_color; ?>; font-weight: bold;">
                        <?php echo $status_text; ?>
                    </span>
                </td>
                <td style="width: 50%; text-align: right;">
                    <strong>TANGGAL PEMBAYARAN TERAKHIR:</strong>
                    <?php 
                    echo $payment_data->last_payment_date ? 
                        date('d/m/Y H:i', strtotime($payment_data->last_payment_date)) : 
                        '-';
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <strong>DIPROSES OLEH KASIR:</strong>
                    <?php 
                    echo $payment_data->kasir_names ? 
                        strtoupper($payment_data->kasir_names) : 
                        '-';
                    ?>
                </td>
            </tr>
            <?php if (!$is_lunas && $payment_data->total_paid > 0): ?>
            <tr>
                <td colspan="2">
                    <strong>SISA YANG HARUS DIBAYAR:</strong>
                    <?php echo number_format($total_bill - $payment_data->total_paid, 0, ',', '.'); ?>
                </td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
            -->
    <!-- End Status Pembayaran -->

    <!-- Signature dipindahkan ke halaman 2 -->


            </div>
        </div>
    </div>

</div>


<style>
.signature-table {
    width: 100%;
    margin-top: 2px;
    margin-bottom: 10px;
    font-size:11px;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.signature-table td {
    width: 50%;
    padding: 20px;
    text-align: center;
    vertical-align: top;
}

.signature-cell {
    height: 120px;
    position: relative;
}

.signature-line {
    border-bottom: 0px solid #000;
    width: 80%;
    margin: 60px auto 5px auto;
}
</style>
